import { request } from '@umijs/max';

// 扩展的评价数据接口
interface ReviewWithRelations extends API.Review {
  createdAt?: string;
  customer?: API.Customer;
  employee?: API.Employee;
}

/** 查询评价列表  GET /reviews */
export async function index(params: Record<string, any>) {
  return request<API.ResType<{ total?: number; list?: ReviewWithRelations[] }>>(
    '/reviews',
    {
      method: 'GET',
      params,
    },
  );
}

/** 创建评价  POST /reviews */
export async function create(body: Omit<API.Review, 'id'>) {
  return request<API.ResType<API.Review>>('/reviews', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 按ID查询评价  GET /reviews/:id */
export async function show(id: number) {
  return request<API.ResType<API.Review>>(`/reviews/${id}`, {
    method: 'GET',
  });
}

/** 修改评价  PUT /reviews/:id */
export async function update(id: number, body: Partial<API.Review>) {
  return request<API.ResType<unknown>>(`/reviews/${id}`, {
    method: 'PUT',
    headers: {
      'Content-Type': 'application/json',
    },
    data: body,
  });
}

/** 删除评价  DELETE /reviews/:id */
export async function remove(id: number) {
  return request<API.ResType<unknown>>(`/reviews/${id}`, {
    method: 'DELETE',
  });
}

/** 获取评价统计数据  GET /reviews/statistics */
export async function statistics(params?: {
  startDate?: string;
  endDate?: string;
}) {
  console.log('=== reviews.statistics API调用 ===');
  console.log('API params:', params);

  const result = await request<
    API.ResType<{
      totalReviews: number;
      todayReviews: number;
      averageRating: number;
      positiveRate: number;
      thisWeekReviews: number;
      thisMonthReviews: number;
    }>
  >('/reviews/statistics', {
    method: 'GET',
    params,
  });

  console.log('=== reviews.statistics API响应 ===');
  console.log('API result:', result);

  return result;
}

/** 获取评价趋势数据  GET /reviews/trend */
export async function trend(params: { startDate: string; endDate: string }) {
  return request<
    API.ResType<
      {
        date: string;
        reviews: number;
        averageRating: number;
      }[]
    >
  >('/reviews/trend', {
    method: 'GET',
    params,
  });
}

/** 获取评分分布数据  GET /reviews/rating-distribution */
export async function ratingDistribution(params?: {
  startDate?: string;
  endDate?: string;
}) {
  return request<
    API.ResType<
      {
        rating: number;
        count: number;
        percentage: number;
      }[]
    >
  >('/reviews/rating-distribution', {
    method: 'GET',
    params,
  });
}

/** 获取员工评分排行  GET /reviews/employee-ranking */
export async function employeeRanking(params?: { limit?: number }) {
  return request<
    API.ResType<
      {
        employeeId: number;
        employeeName: string;
        averageRating: number;
        reviewCount: number;
        level: number;
      }[]
    >
  >('/reviews/employee-ranking', {
    method: 'GET',
    params,
  });
}
